import ddddocr
import cv2
import numpy as np

det = ddddocr.DdddOcr(det=True, ocr=True)

with open("1.png", 'rb') as f:
    image = f.read()

# Get bounding boxes
bboxes = det.detection(image)
print("Detected bboxes:", bboxes)

# Load image for processing
im = cv2.imread("1.png")
original_im = im.copy()

# Extract and recognize text from each bounding box
recognized_chars = []
for i, bbox in enumerate(bboxes):
    x1, y1, x2, y2 = bbox
    
    # Crop the character region
    cropped = original_im[y1:y2, x1:x2]
    
    # Convert to bytes for OCR
    _, buffer = cv2.imencode('.png', cropped)
    cropped_bytes = buffer.tobytes()
    
    # Recognize the character
    char = det.classification(cropped_bytes)
    recognized_chars.append({
        'char': char,
        'bbox': bbox,
        'center': ((x1 + x2) // 2, (y1 + y2) // 2)
    })
    
    print(f"Box {i}: {char} at {bbox}")

# Find instruction text (usually contains "请点击")
instruction_text = ""
instruction_chars = []
clickable_chars = []

for item in recognized_chars:
    if "请" in item['char'] or "点" in item['char'] or "击" in item['char']:
        instruction_text += item['char']
    else:
        clickable_chars.append(item)

print(f"Instruction text: {instruction_text}")

# Extract characters to click from instruction
# Assuming format like "请点击文字" where "文字" are the chars to click
if "请点击" in instruction_text:
    chars_to_click = instruction_text.replace("请点击", "")
    print(f"Characters to click in order: {list(chars_to_click)}")
    
    # Generate clicking sequence
    click_sequence = []
    for target_char in chars_to_click:
        for item in clickable_chars:
            if target_char in item['char']:
                click_sequence.append(item)
                break
    
    print("Clicking sequence:")
    for i, item in enumerate(click_sequence):
        print(f"{i+1}. Click '{item['char']}' at center {item['center']}")
        
        # Draw numbered circles on result image
        cv2.circle(im, item['center'], 15, (0, 255, 0), 2)
        cv2.putText(im, str(i+1), (item['center'][0]-5, item['center'][1]+5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

# Draw all bounding boxes
for bbox in bboxes:
    x1, y1, x2, y2 = bbox
    cv2.rectangle(im, (x1, y1), (x2, y2), (0, 0, 255), 2)

cv2.imwrite("result.jpg", im)
